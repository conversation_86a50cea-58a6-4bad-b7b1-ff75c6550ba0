package so.appio.app.ui.screens

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreHoriz
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import so.appio.app.R
import so.appio.app.ui.components.FeedbackModal
import so.appio.app.ui.theme.AppioAppTheme
import androidx.compose.runtime.LaunchedEffect
import so.appio.app.utils.FeatureFlagManager
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalLayoutDirection
import android.content.res.Configuration

@Composable
fun IntroScreen(
    modifier: Modifier = Modifier,
    context: Context,
    onTryDemoService: () -> Unit,
    onContinueSetup: () -> Unit,
    onValidQRCodeURL: ((String) -> Unit)? = null,
) {
    var showQRScanScreen by remember { mutableStateOf(false) }
    var showFeedbackModal by remember { mutableStateOf(false) }
    val orientation = LocalConfiguration.current.orientation

    if (showQRScanScreen) {
        QRScanScreen(
            orientation = orientation,
            onBackClick = {
                showQRScanScreen = false
            },
            onValidAppioUrl = { url ->
                showQRScanScreen = false
                onValidQRCodeURL?.invoke(url)
            }
        )
    } else {
        IntroContent(
            context = context,
            orientation = orientation,
            onTryDemoService = onTryDemoService,
            onContinueSetup = onContinueSetup,
            onScanQRCode = {
                showQRScanScreen = true
            },
            onFeedbackClick = {
                showFeedbackModal = true
            },
            modifier = modifier
        )
    }

    // Show feedback modal when requested
    if (showFeedbackModal) {
        FeedbackModal(
            serviceId = null,
            deviceId = null,
            onDismiss = { showFeedbackModal = false }
        )
    }
}

@Composable
private fun IntroContent(
    modifier: Modifier = Modifier,
    context: Context,
    orientation: Int,
    onTryDemoService: () -> Unit,
    onContinueSetup: () -> Unit,
    onScanQRCode: () -> Unit,
    onFeedbackClick: () -> Unit,
) {
    var ffIntro by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        ffIntro = FeatureFlagManager(context).get<String>("intro") == "A"
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // Three dots button in top right
        IconButton(
            onClick = onFeedbackClick,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(
                    top = WindowInsets.statusBars.asPaddingValues().calculateTopPadding() + 8.dp,
                    end = 16.dp + if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                        WindowInsets.navigationBars.asPaddingValues().calculateEndPadding(LocalLayoutDirection.current)
                    } else {
                        0.dp
                    }
                )
        ) {
            Icon(
                imageVector = Icons.Filled.MoreHoriz,
                contentDescription = "Feedback",
                tint = MaterialTheme.colorScheme.onSurface
            )
        }

        // Main content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    bottom = 24.dp + WindowInsets.navigationBars.asPaddingValues().calculateBottomPadding(),
                    start = 24.dp,
                    end = 24.dp,
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Spacer(modifier = Modifier.padding(32.dp + WindowInsets.navigationBars.asPaddingValues().calculateTopPadding()))
        } else {
            Spacer(modifier = Modifier.weight(.5f))
        }

        if (orientation != Configuration.ORIENTATION_LANDSCAPE) {
            // Illustration - Simple connection graphic
            Box(
                modifier = Modifier.padding(bottom = 32.dp),
                contentAlignment = Alignment.Center
            ) {
                // Connection illustration using the plug vector drawable
                Icon(
                    painter = painterResource(id = R.drawable.ic_plug),
                    contentDescription = "Illustration of connection",
                    tint = MaterialTheme.colorScheme.onSurface,
                )
            }
        }
        
        // Main title
        Text(
            text = buildAnnotatedString {
                append("Let's get you\n")
                withStyle(style = SpanStyle(color = MaterialTheme.colorScheme.primary)) {
                    append("Connected")
                }
            },
            style = MaterialTheme.typography.headlineLarge.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 32.sp,
                lineHeight = 40.sp
            ),
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        if (ffIntro) {
            Text(
                text = buildAnnotatedString {
                    append("To get started, connect to a service that uses Appio.\nAppio is a companion app for ")
                    withStyle(style = SpanStyle(color = MaterialTheme.colorScheme.primary)) {
                        append("https://appio.so")
                    }
                },
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontSize = 16.sp,
                    lineHeight = 24.sp
                ),
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Spacer(modifier = Modifier.padding(32.dp))
        } else {
            Spacer(modifier = Modifier.weight(.3f))
        }

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.Center,
                ) {
                    QRScanButton(onScanQRCode = onScanQRCode)
                }

                ButtonsHorizontalOrSeparator()

                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.Center
                ) {
                    MainButton(
                        ffIntro = ffIntro,
                        onTryDemoService = onTryDemoService,
                        onContinueSetup = onContinueSetup,
                    )
                }
            }
        } else {
            MainButton(
                ffIntro = ffIntro,
                onTryDemoService = onTryDemoService,
                onContinueSetup = onContinueSetup,
            )

            Spacer(modifier = Modifier.height(16.dp))

            ButtonsVerticalOrSeparator()

            QRScanButton(onScanQRCode = onScanQRCode)
        }
        }
    }
}

@Composable
private fun MainButton(
    ffIntro: Boolean,
    onTryDemoService: () -> Unit,
    onContinueSetup: () -> Unit,
) {
    // Description text
    if (ffIntro) {
        // Primary button - Try demo service
        Button(
            onClick = onTryDemoService,
            elevation = ButtonDefaults.buttonElevation(
                defaultElevation = 5.dp
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp)
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.onPrimary
            )
        ) {
            Text(
                text = "Try demo service",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.SemiBold,
                    fontSize = 16.sp
                )
            )
        }
    } else {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // Primary button - Continue setup
            Button(
                onClick = onContinueSetup,
                elevation = ButtonDefaults.buttonElevation(
                    defaultElevation = 5.dp
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp)
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary
                )
            ) {
                Text(
                    text = "Continue setup",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 16.sp
                    )
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "If you came from a browser",
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun QRScanButton(
    onScanQRCode: () -> Unit,
) {
    // Secondary button - Scan QR code
    TextButton(
        onClick = onScanQRCode,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 32.dp)
            .height(56.dp),
    ) {
        Text(
            text = "Scan the QR code",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Medium,
                fontSize = 16.sp
            ),
            color = MaterialTheme.colorScheme.primary
        )
    }
}

@Composable
private fun ButtonsHorizontalOrSeparator() {
    // "or" text with lines
    Column(
        modifier = Modifier
            .fillMaxHeight()
            .padding(horizontal = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Top line
        Box(
            modifier = Modifier
                .weight(1f)
                .width(1.dp)
                .background(MaterialTheme.colorScheme.outlineVariant)
        )

        // "or" text
        Text(
            text = "or",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        // Bottom line
        Box(
            modifier = Modifier
                .weight(1f)
                .width(1.dp)
                .background(MaterialTheme.colorScheme.outlineVariant)
        )
    }
}

@Composable
private fun ButtonsVerticalOrSeparator() {
    // "or" text with lines
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left line
        Box(
            modifier = Modifier
                .weight(1f)
                .height(1.dp)
                .background(MaterialTheme.colorScheme.outlineVariant)
        )

        // "or" text
        Text(
            text = "or",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        // Right line
        Box(
            modifier = Modifier
                .weight(1f)
                .height(1.dp)
                .background(MaterialTheme.colorScheme.outlineVariant)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun IntroScreenPreview() {
    AppioAppTheme {
        // Note: Preview won't show feature flag behavior since context is not available
        Column {
            Text("Preview - Feature flag logic requires real context")
        }
    }
}
