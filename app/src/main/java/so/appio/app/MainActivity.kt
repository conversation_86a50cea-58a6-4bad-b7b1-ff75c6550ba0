package so.appio.app

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.activity.viewModels
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import so.appio.app.ui.screens.IntroScreen
import so.appio.app.ui.screens.LoadingScreen
import so.appio.app.ui.screens.MainViewModel
import so.appio.app.ui.screens.NotificationScreen
import so.appio.app.ui.screens.ServiceScreen
import so.appio.app.ui.screens.ServicesListScreen
import so.appio.app.ui.theme.AppioAppTheme
import so.appio.app.utils.BrowserUtils
import so.appio.app.utils.IntentHandler
import so.appio.app.utils.MySplashScreen
import so.appio.app.utils.NotificationPermissionManager
import so.appio.app.data.AppDataStore

class MainActivity : ComponentActivity() {

    companion object {
        internal const val DEMO_URL = "https://demo.appio.so/?platform=android&app=true"
        internal const val APP_URL = "https://app.appio.so/?platform=android&app=true" // don't use /android? because that would open the app directly. we need to open browsed first to give it a chance to read cookies
        internal const val TAG = "LOG:MainActivity"
    }

    // ViewModel holds app's state and business logic
    private val viewModel: MainViewModel by viewModels()

    // Notification permission manager - MUST be initialized early in lifecycle
    private lateinit var notificationPermissionManager: NotificationPermissionManager

    // Track the last intent source to prevent race conditions in onResume()
    private var lastIntentSource: IntentHandler.IntentSource = IntentHandler.IntentSource.NORMAL_LAUNCH

    // Public getter for UI components
    fun getNotificationPermissionManager(): NotificationPermissionManager = notificationPermissionManager

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        // Debug
        // DeviceInfoManager(this).logDeviceInfo()

        MySplashScreen.initialize(installSplashScreen(), viewModel)

        // Call after splash screen
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate called")

        // Init notification permission manager EARLY (before STARTED state)
        notificationPermissionManager = NotificationPermissionManager(this, AppDataStore(this), (application as MyApplication).deviceRepository)

        // Init other components
        viewModel.initialize(this)

        // Load feature flags during splash screen (blocking for all intent types)
        viewModel.loadFeatureFlags()

        // Track initial intent source for race condition prevention
        lastIntentSource = IntentHandler.detectIntentSource(intent)
        Log.d(TAG, "onCreate - detected intent source: $lastIntentSource")

        // Handle install referrer and only after intents. To prevent install referrer and fingerprint check collision
        viewModel.handleInstallReferrer(this, {
            viewModel.handleIntent(intent)
        })

        enableEdgeToEdge()
        setContent {
            AppioAppTheme {
                val currentService by viewModel.currentService.collectAsState()
                val services by viewModel.services.collectAsState()
                val isLoading by viewModel.isLoading.collectAsState()
                val currentNotification by viewModel.currentNotification.collectAsState()

                when {
                    isLoading -> {
                        Log.d(TAG, "Showing LoadingScreen")
                        // Show loading screen while processing
                        LoadingScreen()
                    }
                    currentNotification != null -> {
                        Log.d(TAG, "Showing NotificationScreen")
                        NotificationScreen(
                            notification = currentNotification!!,
                            onBackClick = {
                                Log.d(TAG, "Back button clicked - returning to service")
                                viewModel.navigateBackFromNotification()
                            }
                        )
                    }
                    currentService != null -> {
                        Log.d(TAG, "Showing ServiceScreen")
                        val service = currentService!!
                        val serviceViewModel = viewModel.getServiceViewModel(service.id)

                        ServiceScreen(
                            service = service,
                            viewModel = serviceViewModel,
                            onNotificationClick = { notification ->
                                Log.d(TAG, "Notification clicked: ${notification.id}")
                                viewModel.navigateToNotification(notification)
                            },
                            onBackClick = if (services.size > 1) {
                                {
                                    Log.d(TAG, "Back button clicked - returning to services list")
                                    viewModel.navigateToAllServices()
                                }
                            } else null,
                        )
                    }
                    services.size > 1 -> {
                        Log.d(TAG, "Showing ServicesListScreen")
                        ServicesListScreen(
                            services = services,
                            onServiceClick = { service ->
                                Log.d(TAG, "Service selected from list: ${service.id}")
                                viewModel.navigateToService(service)
                            }
                        )
                    }
                    else -> {
                        Log.d(TAG, "Showing IntroScreen")
                        // Show IntroScreen for NoAction
                        IntroScreen(
                            context = this@MainActivity,
                            onTryDemoService = {
                                BrowserUtils.openUrl(this@MainActivity, DEMO_URL)
                            },
                            onContinueSetup = {
                                BrowserUtils.openUrl(this@MainActivity, APP_URL)
                            },
                            onValidQRCodeURL = { url ->
                                Log.d(TAG, "Valid Appio URL from QR scan: $url")
                                viewModel.processStartupUrl(url, viewModel::intentDone)
                            }
                        )
                    }
                }
            }
        }
    }

    // Called if the Activity is already running and is launched again
    // Called before onResume - this helps us detect and prevent refreshing all services and prevent race condition
    // (e.g., from another notification tap while the app is in the background but not killed)
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        lastIntentSource = IntentHandler.detectIntentSource(intent)
        Log.d(TAG, "onNewIntent called with intent: $intent, detected source: $lastIntentSource")
        viewModel.handleIntent(intent)
    }

    // Called when the activity is becoming visible.
    override fun onStart() {
        super.onStart()
        Log.d(TAG, "onStart called")

        // Call ViewModel onStart
        viewModel.onStart()
    }

    // Called when the activity resumes and becomes interactive.
    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume called with lastIntentSource: $lastIntentSource")

        // Check and update notification status when app resumes
        // This detects changes made in system settings when user returns to app
        notificationPermissionManager.checkAndUpdateNotificationStatus()

        // Check if device data is out of sync and sync with server if needed
        // This handles cases where previous API calls failed
        (application as MyApplication).deviceRepository.checkAndSyncIfNeeded()

        // Only refresh all services for normal launches to prevent race condition with deep link service registration
        if (lastIntentSource == IntentHandler.IntentSource.NORMAL_LAUNCH) {
            Log.d(TAG, "Normal launch detected - refreshing services")
            (application as MyApplication).refreshServices()
        }
        // Reset intent source for next resume cycle
        lastIntentSource = IntentHandler.IntentSource.NORMAL_LAUNCH
    }

    // Called when the activity is no longer visible. App is hidden, minimized, or another app is in the foreground.
    // App is still in memory and can be quickly resumed.
    override fun onStop() {
        super.onStop()
        viewModel.onStop()
    }


}