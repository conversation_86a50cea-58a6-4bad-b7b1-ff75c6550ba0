package so.appio.app.utils

import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.util.Log
import so.appio.app.ui.screens.MainViewModel

/**
 * Handles intent detection and processing for MainActivity
 */
object IntentHandler {

    private const val TAG = "LOG:IntentHandler"

    // custom intent action for cases where we can't overwrite native `action`
    const val INTENT_ACTION = "intent_action"

    // Intent extra keys
    const val KEY_SERVICE_ID = "service_id"
    const val KEY_NOTIFICATION_ID = "notification_id"
    const val KEY_URL = "url"

    enum class IntentAction(val value: String) {
        NOTIFICATION("notification"),
        WIDGET("widget"),
    }

    enum class IntentSource {
        NOTIFICATION,
        WIDGET,
        SHORTCUT,
        DEEP_LINK,
        NORMAL_LAUNCH,
        OTHER
    }

    /**
     * Detects the source of an intent based on its properties
     */
    fun detectIntentSource(intent: Intent): IntentSource {
        val extras = intent.extras
        val action = intent.action ?: extras?.getString(INTENT_ACTION, "")
        val categories = intent.categories

        Log.d(TAG, "Intent action: $action")
        Log.d(TAG, "Intent categories: $categories")
        extras?.let {
            it.keySet().forEach { key ->
                val value = when {
                    it.getString(key) != null -> it.getString(key)
                    it.getInt(key, Int.MIN_VALUE) != Int.MIN_VALUE -> it.getInt(key)
                    it.getBoolean(key, false) != false || it.containsKey(key) -> it.getBoolean(key)
                    it.getLong(key, Long.MIN_VALUE) != Long.MIN_VALUE -> it.getLong(key)
                    else -> "null"
                }
                Log.d(TAG, "Extra: $key = $value")
            }
        }

        // Check for our Notification
        if (action == IntentAction.NOTIFICATION.value) {
            return IntentSource.NOTIFICATION
        }

        // Check for widget click
        if (action == IntentAction.WIDGET.value) {
            return IntentSource.WIDGET
        }

        // Check for deep link
        if (intent.data != null) {
            return IntentSource.DEEP_LINK
        }

        // Check for app deep link
        if (action == Intent.ACTION_VIEW && categories?.contains(Intent.CATEGORY_BROWSABLE) == true) {
            return IntentSource.DEEP_LINK
        }

        // Check for shortcut (static or dynamic)
        if (action == Intent.ACTION_VIEW && extras?.containsKey(Intent.EXTRA_SHORTCUT_ID) == true ||
            extras?.containsKey("shortcut_id") == true // custom key defined by us
        ) {
            return IntentSource.SHORTCUT
        }

        // Check for normal app launch
        if (action == Intent.ACTION_MAIN &&
            categories?.contains(Intent.CATEGORY_LAUNCHER) == true
        ) {
            return IntentSource.NORMAL_LAUNCH
        }

        return IntentSource.OTHER
    }

    /**
     * Handles Notification intents
     */
    fun handleNotificationIntent(intent: Intent, mainViewModel: MainViewModel) {
        Log.d(TAG, "Handling Notification intent")

        val extras = intent.extras
        if (extras == null) {
            mainViewModel.intentDone()
            return
        }

        // Check for notification_id (our app's notification ID) first
        val notificationId = extras.getString(KEY_NOTIFICATION_ID)
        if (notificationId != null) {
            Log.d(TAG, "Navigating to specific notification: $notificationId")

            // Cancel the system notification using the notification ID hash
            val context = mainViewModel.getContext()
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(notificationId.hashCode())

            mainViewModel.navigateToNotificationById(notificationId)
            mainViewModel.intentDone()
            return
        }

        // Fallback: Check for service_id in notification extras
        val serviceId = extras.getString(KEY_SERVICE_ID)
        if (serviceId != null) {
            Log.d(TAG, "Navigating to service: $serviceId")
            mainViewModel.navigateToServiceById(serviceId)
        }

        mainViewModel.intentDone()
    }

    /**
     * Handles widget intents
     */
    fun handleWidgetIntent(intent: Intent, mainViewModel: MainViewModel) {
        Log.d(TAG, "Handling widget intent")

        val extras = intent.extras
        val serviceId = extras?.getString(KEY_SERVICE_ID)

        Log.d(TAG, "Widget Service ID: $serviceId")

        val url = extras?.getString(KEY_URL)
        if (UrlValidator.isValidBrowserUrl(url)) {
            Log.d(TAG, "Widget opening URL: $url")
            val context = mainViewModel.getContext()
            BrowserUtils.openUrl(context, url!!)
            return
        }

        // Handle service ID directly or process URL
        if (serviceId != null) {
            mainViewModel.navigateToServiceById(serviceId)
        }

        mainViewModel.intentDone()
    }

    /**
     * Handles shortcut intents
     */
    fun handleShortcutIntent(intent: Intent, mainViewModel: MainViewModel) {
        Log.d(TAG, "Handling shortcut intent")

        val extras = intent.extras
        if (extras == null) {
            mainViewModel.intentDone()
            return
        }

        val shortcutId =
            extras.getString("shortcut_id") ?: extras.getString("android.intent.extra.shortcut.ID")

        Log.d(TAG, "Shortcut ID: $shortcutId")

        // Handle shortcut-specific actions
        when (shortcutId) {
            "compose" -> Log.d(TAG, "Open compose screen")
            "settings" -> Log.d(TAG, "Open settings screen")
            // Add your shortcut handling here
        }

        // Check for service_id in shortcut extras
        val serviceId = extras.getString(KEY_SERVICE_ID)
        if (serviceId != null) {
            mainViewModel.navigateToServiceById(serviceId)
        }

        mainViewModel.intentDone()
    }

    /**
     * Handles deep link intents
     */
    fun handleDeepLinkIntent(intent: Intent, mainViewModel: MainViewModel) {
        Log.d(TAG, "Handling deep link intent")

        val data = intent.data
        if (data == null) {
            Log.d(TAG, "No data in deep link intent")
            mainViewModel.intentDone()
            return
        }

        val url = data.toString()
        Log.d(TAG, "Deep link URL: $url")
        mainViewModel.processStartupUrl(url, mainViewModel::intentDone)
    }

    /**
     * Handles other/unknown intent types
     */
    fun handleOtherIntent(intent: Intent, mainViewModel: MainViewModel) {
        Log.d(TAG, "Handling other intent type")

        val extras = intent.extras
        if (extras == null) {
            mainViewModel.intentDone()
            return
        }

        // Log all extras for debugging unknown intent types
        Log.d(TAG, "Unknown intent extras for investigation:")
        mainViewModel.intentDone()
    }
}
